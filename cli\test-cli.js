#!/usr/bin/env node

// 测试CLI程序的基本功能
const EmailStepsCLI = require('./email-steps-cli');

async function test() {
  console.log('🧪 测试CLI程序...');
  
  try {
    // 测试邮箱验证
    const cli = new EmailStepsCLI();
    
    console.log('测试邮箱验证:');
    console.log('<EMAIL>:', cli.validateEmail('<EMAIL>'));
    console.log('invalid-email:', cli.validateEmail('invalid-email'));
    
    // 测试历史记录
    console.log('\n测试历史记录功能:');
    await cli.showHistory();
    
    console.log('\n✅ 基本功能测试通过');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

if (require.main === module) {
  test();
}