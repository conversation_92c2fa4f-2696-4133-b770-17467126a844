.loadingContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  z-index: 9999;
  backdrop-filter: blur(10px);
}

.loadingSpinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 30px 50px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
  position: relative;
  z-index: 10;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.loadingText {
  font-size: 20px;
  color: white;
  margin: 0;
  font-weight: 500;
  letter-spacing: 1px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.loadingProgress {
  height: 4px;
  background: white;
  border-radius: 10px;
  width: 0;
  margin-top: 10px;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 背景形状 */
.backgroundShapes {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.shape {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.4;
}

.shape1 {
  width: 300px;
  height: 300px;
  background: rgba(255, 0, 128, 0.3);
  top: 10%;
  left: 10%;
}

.shape2 {
  width: 350px;
  height: 350px;
  background: rgba(0, 128, 255, 0.3);
  bottom: 10%;
  right: 10%;
}

.shape3 {
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 0, 0.3);
  bottom: 30%;
  left: 30%;
}

/* 添加一些装饰元素 */
.loadingSpinner::before {
  content: "";
  position: absolute;
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% { transform: scale(0.8); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.5; }
  100% { transform: scale(0.8); opacity: 0.3; }
} 