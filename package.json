{"name": "zepp-life-steps", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "cli": "node cli/email-steps-cli.js", "cli:history": "node cli/email-steps-cli.js --history"}, "bin": {"zepp-email-steps": "./cli/email-steps-cli.js"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.8.6", "axios": "^1.8.4", "framer-motion": "^12.6.3", "gray-matter": "^4.0.3", "moment": "^2.30.1", "next": "13.4.19", "next-mdx-remote": "^5.0.0", "next-seo": "^6.6.0", "raw-loader": "^4.0.2", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "^10.1.0"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.9"}}