# Zepp Life 邮箱步数修改 CLI 工具

## 功能特点

- ✅ 专门支持邮箱账号登录
- 📅 支持指定日期修改步数
- 📝 自动保存历史记录
- 🔒 安全的密码输入
- 🎯 交互式和命令行两种模式

## 安装使用

### 1. 交互式模式
```bash
npm run cli
```

### 2. 命令行模式
```bash
# 基本用法
npm run cli -- -e <EMAIL> -p password123 -s 25000

# 指定日期
npm run cli -- -e <EMAIL> -p password123 -s 25000 -d 2024-01-15

# 查看历史记录
npm run cli:history
```

### 3. 全局安装
```bash
npm install -g .
zepp-email-steps -e <EMAIL> -p password123 -s 25000
```

## 参数说明

| 参数 | 简写 | 说明 | 必填 |
|------|------|------|------|
| --email | -e | 邮箱地址 | 是 |
| --password | -p | 密码 | 是 |
| --steps | -s | 目标步数 | 否 (默认随机) |
| --date | -d | 日期 (YYYY-MM-DD) | 否 (默认今天) |
| --history | -h | 显示历史记录 | - |
| --help | - | 显示帮助 | - |

## 历史记录

程序会自动保存每次操作的历史记录到 `cli/history.json`，包括：
- 邮箱地址
- 修改的步数
- 目标日期
- 操作时间
- 成功/失败状态