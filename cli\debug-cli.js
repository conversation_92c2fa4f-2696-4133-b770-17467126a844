#!/usr/bin/env node

const readline = require('readline');
const moment = require('moment');

// 引入现有的ZeppLifeSteps模块
const zeppLifeSteps = require('../pages/api/ZeppLifeSteps');

class DebugCLI {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  // 邮箱格式验证
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // 获取用户输入
  async prompt(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }

  // 调试版本的步数修改功能 - 完全模仿test-fix.js
  async updateStepsDebug(email, password, steps, targetDate) {
    try {
      console.log('\n=== 调试版本 - 完全模仿test-fix.js ===');
      console.log('📧 邮箱:', email);
      console.log('👟 目标步数:', steps);
      console.log('📅 目标日期:', targetDate);
      console.log('');
      
      // 登录
      console.log('🔐 开始登录...');
      const { loginToken, userId } = await zeppLifeSteps.login(email, password);
      console.log('✅ 登录成功，用户ID:', userId);
      
      // 获取app token
      console.log('🔑 获取应用令牌...');
      const appToken = await zeppLifeSteps.getAppToken(loginToken);
      console.log('✅ 令牌获取成功');
      
      // 修改步数 - 完全相同的调用方式
      console.log('📊 开始修改步数...');
      const result = await zeppLifeSteps.updateSteps(loginToken, appToken, steps, targetDate);
      
      console.log('\n🎉 测试成功！');
      console.log('📋 修改结果:', JSON.stringify(result, null, 2));
      console.log('\n✅ 步数修改功能已修复，现在应该能正常工作了！');
      
      return result;
    } catch (error) {
      console.error('\n❌ 测试失败:', error.message);
      console.error('📋 错误详情:', error.stack);
      throw error;
    }
  }

  // 简化的交互式运行
  async run() {
    console.log('🚀 调试版本 - Zepp Life 邮箱步数修改工具');
    console.log('─'.repeat(50));
    console.log('⚠️  此版本用于调试，完全模仿test-fix.js的调用方式');
    console.log('─'.repeat(50));

    try {
      // 获取邮箱
      let email;
      do {
        email = await this.prompt('📧 请输入邮箱地址: ');
        if (!this.validateEmail(email)) {
          console.log('❌ 邮箱格式不正确，请重新输入');
        }
      } while (!this.validateEmail(email));

      console.log(`✅ 邮箱格式正确: ${email}`);

      // 获取密码 (明文输入)
      const password = await this.prompt('🔒 请输入密码 (明文显示): ');
      
      if (!password || password.trim().length === 0) {
        console.log('❌ 密码不能为空');
        return;
      }
      
      console.log('✅ 密码已输入');

      // 获取步数
      const stepsInput = await this.prompt('👟 请输入目标步数 (回车使用随机步数): ');
      const steps = stepsInput.trim() ? parseInt(stepsInput) : Math.floor(Math.random() * 1000) + 9000;

      if (isNaN(steps) || steps < 0) {
        console.log('❌ 步数格式不正确');
        return;
      }

      // 获取日期
      const dateInput = await this.prompt('📅 请输入日期 (YYYY-MM-DD，回车使用今天): ');
      const targetDate = dateInput.trim() || moment().format('YYYY-MM-DD');

      if (!moment(targetDate, 'YYYY-MM-DD', true).isValid()) {
        console.log('❌ 日期格式不正确');
        return;
      }

      console.log(`\n📋 确认信息:`);
      console.log(`   邮箱: ${email}`);
      console.log(`   步数: ${steps}`);
      console.log(`   日期: ${targetDate}`);

      const confirm = await this.prompt('\n✅ 确认执行? (y/N): ');
      if (confirm.toLowerCase() !== 'y') {
        console.log('❌ 操作已取消');
        return;
      }

      console.log('\n🚀 开始执行操作...\n');
      
      // 使用调试版本的函数，完全模仿test-fix.js
      await this.updateStepsDebug(email, password, steps, targetDate);

    } catch (error) {
      console.error('\n💥 程序执行出错:');
      console.error('错误信息:', error.message);
      console.error('错误堆栈:', error.stack);
    } finally {
      this.rl.close();
    }
  }
}

// 主程序入口
async function main() {
  const cli = new DebugCLI();
  await cli.run();
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

module.exports = DebugCLI;
